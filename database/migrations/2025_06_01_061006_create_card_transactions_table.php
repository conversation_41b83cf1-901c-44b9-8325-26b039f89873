<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('card_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('telco')->nullable();
            $table->string('card_name')->nullable();
            $table->string('serial', 20);
            $table->string('pin', 20);
            $table->integer('declared_value');
            $table->integer('actual_value')->nullable();
            $table->decimal('exchange_rate', 5, 2);
            $table->integer('credited_amount')->nullable();
            $table->string('provider_transaction_id')->nullable();
            $table->enum('status', ['pending', 'processing', 'success', 'failed', 'invalid_card'])->default('pending');
            $table->json('provider_response')->nullable();
            $table->string('failure_reason')->nullable();
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index(['provider_transaction_id']);
            $table->unique(['serial', 'pin'], 'unique_card_combination');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('card_transactions');
    }
};
