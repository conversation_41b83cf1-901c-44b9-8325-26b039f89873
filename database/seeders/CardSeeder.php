<?php

namespace Database\Seeders;

use App\Models\CardType;
use App\Models\CardDenomination;
use Illuminate\Database\Seeder;

class CardSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $cardTypes = [
            [
                'name' => 'Thẻ Garena',
                'slug' => 'garena',
                'image_url' => 'https://napsieutoc.com/files/the-cao/the-garena.png',
                'discount' => 3,
                'values' => [10000, 20000, 50000, 100000, 200000, 500000],
            ],
            [
                'name' => 'Thẻ Zing',
                'slug' => 'zing',
                'image_url' => 'https://napsieutoc.com/files/the-cao/the-zing.png',
                'discount' => 4,
                'values' => [10000, 20000, 50000, 100000, 200000, 500000],
            ],
            [
                'name' => 'Thẻ VCoin',
                'slug' => 'vcoin',
                'image_url' => 'https://napsieutoc.com/files/the-cao/the-vcoin.png',
                'discount' => 5,
                'values' => [10000, 20000, 50000, 100000, 200000, 500000, 1000000, 2000000],
            ],
            [
                'name' => 'Thẻ Viettel',
                'slug' => 'viettel',
                'image_url' => 'https://napsieutoc.com/files/the-cao/the-viettel.png',
                'discount' => 2,
                'values' => [10000, 20000, 50000, 100000, 200000, 500000],
            ],
            [
                'name' => 'Thẻ Vinaphone',
                'slug' => 'vinaphone',
                'image_url' => 'https://napsieutoc.com/files/the-cao/the-vinaphone.png',
                'discount' => 3,
                'values' => [10000, 20000, 50000, 100000, 200000, 500000],
            ],
            [
                'name' => 'Thẻ Mobifone',
                'slug' => 'mobifone',
                'image_url' => 'https://napsieutoc.com/files/the-cao/the-mobifone.png',
                'discount' => 3,
                'values' => [10000, 20000, 50000, 100000, 200000, 500000],
            ],
            [
                'name' => 'Thẻ Appota',
                'slug' => 'appota',
                'image_url' => 'https://napsieutoc.com/files/the-cao/the-appota.png',
                'discount' => 4,
                'values' => [10000, 20000, 50000, 100000, 200000, 500000],
            ],
            [
                'name' => 'Thẻ Funcard',
                'slug' => 'funcard',
                'image_url' => 'https://napsieutoc.com/files/the-cao/the-funcard.png',
                'discount' => 4,
                'values' => [10000, 20000, 50000, 100000, 200000, 500000],
            ],
        ];

        foreach ($cardTypes as $cardTypeData) {
            $cardType = CardType::create([
                'name' => $cardTypeData['name'],
                'slug' => $cardTypeData['slug'],
                'image_url' => $cardTypeData['image_url'],
                'status' => true,
            ]);

            foreach ($cardTypeData['values'] as $value) {
                CardDenomination::create([
                    'card_type_id' => $cardType->id,
                    'value' => $value,
                    'discount_percentage' => $cardTypeData['discount'],
                    'status' => true,
                ]);
            }
        }
    }
}
