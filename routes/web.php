<?php

use App\Http\Controllers\CardController;
use App\Http\Controllers\CardExchangeController;
use App\Http\Controllers\CardExchangeWebhookController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\WebhookController;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Home');
})->name('home');

Route::get('mua-the', [CardController::class, 'index'])->name('cards.index');
Route::post('orders', [OrderController::class, 'store'])->name('orders.store');
Route::get('don-hang/{order:order_number}', [OrderController::class, 'show'])->name('orders.show');

Route::post('webhooks/sepay', WebhookController::class)
    ->withoutMiddleware(VerifyCsrfToken::class)
    ->name('webhooks.sepay');

Route::post('webhooks/thesieure', CardExchangeWebhookController::class)
    ->withoutMiddleware(VerifyCsrfToken::class)
    ->name('webhooks.thesieure');

Route::middleware('auth')->group(function () {
    Route::get('doi-the-cao', [CardExchangeController::class, 'index'])->name('card-exchange.index');
    Route::post('doi-the-cao', [CardExchangeController::class, 'store'])->name('card-exchange.store');
    Route::get('doi-the-cao/{transaction}', [CardExchangeController::class, 'show'])->name('card-exchange.show');
    Route::get('lich-su-doi-the', [CardExchangeController::class, 'history'])->name('card-exchange.history');
});
