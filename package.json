{"private": true, "type": "module", "scripts": {"build": "vite build", "build:ssr": "vite build && vite build --ssr", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/node": "^22.13.5", "@vue/eslint-config-typescript": "^14.3.0", "eslint": "^9.17.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-vue": "^9.32.0", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "typescript-eslint": "^8.23.0", "vue-tsc": "^2.2.4"}, "dependencies": {"@inertiajs/vue3": "^2.0.0", "@vitejs/plugin-vue": "^5.2.1", "@vueuse/core": "^12.8.2", "ant-design-vue": "^4.2.6", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.0", "typescript": "^5.2.2", "vite": "^6.2.0", "vue": "^3.5.13", "ziggy-js": "^2.4.2"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "lightningcss-linux-x64-gnu": "^1.29.1"}}