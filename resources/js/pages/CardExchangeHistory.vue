<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { 
    HistoryOutlined, 
    SwapOutlined, 
    FilterOutlined,
    CheckCircleOutlined,
    ClockCircleOutlined,
    CloseCircleOutlined,
    ExclamationCircleOutlined,
    EyeOutlined
} from '@ant-design/icons-vue';
import { ref, computed } from 'vue';
import { router } from '@inertiajs/vue3';

interface Transaction {
    id: number;
    status: string;
    card_type: {
        name: string;
        image_url: string;
    };
    denomination: {
        value: number;
    };
    declared_value: number;
    actual_value: number | null;
    exchange_rate: number;
    credited_amount: number | null;
    created_at: string;
    processed_at: string | null;
}

interface PaginationData {
    data: Transaction[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
}

interface Filters {
    status?: string;
    from_date?: string;
    to_date?: string;
}

interface Props {
    transactions: PaginationData;
    filters: Filters;
}

const props = defineProps<Props>();

const statusFilter = ref(props.filters.status || '');
const fromDate = ref(props.filters.from_date || '');
const toDate = ref(props.filters.to_date || '');

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN').format(amount) + ' đ';
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
};

const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('vi-VN');
};

const getStatusTag = (status: string) => {
    switch (status) {
        case 'success':
            return { color: 'success', text: 'Thành công', icon: CheckCircleOutlined };
        case 'pending':
            return { color: 'warning', text: 'Chờ xử lý', icon: ClockCircleOutlined };
        case 'processing':
            return { color: 'processing', text: 'Đang xử lý', icon: ClockCircleOutlined };
        case 'failed':
            return { color: 'error', text: 'Thất bại', icon: CloseCircleOutlined };
        case 'invalid_card':
            return { color: 'error', text: 'Thẻ không hợp lệ', icon: ExclamationCircleOutlined };
        default:
            return { color: 'default', text: 'Không xác định', icon: ClockCircleOutlined };
    }
};

const applyFilters = () => {
    const params: any = {};
    
    if (statusFilter.value) params.status = statusFilter.value;
    if (fromDate.value) params.from_date = fromDate.value;
    if (toDate.value) params.to_date = toDate.value;
    
    router.get(route('card-exchange.history'), params, {
        preserveState: true,
        preserveScroll: true,
    });
};

const clearFilters = () => {
    statusFilter.value = '';
    fromDate.value = '';
    toDate.value = '';
    
    router.get(route('card-exchange.history'), {}, {
        preserveState: true,
        preserveScroll: true,
    });
};

const viewTransaction = (transactionId: number) => {
    router.visit(route('card-exchange.show', transactionId));
};

const columns = [
    {
        title: 'Mã GD',
        dataIndex: 'id',
        key: 'id',
        width: 80,
    },
    {
        title: 'Loại thẻ',
        key: 'card_type',
        width: 150,
    },
    {
        title: 'Mệnh giá',
        key: 'denomination',
        width: 120,
    },
    {
        title: 'Tỷ lệ',
        dataIndex: 'exchange_rate',
        key: 'exchange_rate',
        width: 80,
    },
    {
        title: 'Nhận được',
        key: 'credited_amount',
        width: 120,
    },
    {
        title: 'Trạng thái',
        key: 'status',
        width: 120,
    },
    {
        title: 'Thời gian',
        key: 'created_at',
        width: 150,
    },
    {
        title: 'Thao tác',
        key: 'action',
        width: 100,
    },
];

const totalSuccess = computed(() => {
    return props.transactions.data
        .filter(t => t.status === 'success')
        .reduce((sum, t) => sum + (t.credited_amount || 0), 0);
});

const totalTransactions = computed(() => props.transactions.total);
</script>

<template>
    <AppLayout>
        <div style="background: #f5f5f5; min-height: 100vh; padding: 24px 0">
            <div style="max-width: 1400px; margin: 0 auto; padding: 0 16px">
                <!-- Header -->
                <div style="margin-bottom: 24px">
                    <h1 style="margin-bottom: 8px; color: #1890ff">
                        <HistoryOutlined style="margin-right: 8px" />
                        Lịch Sử Đổi Thẻ Cào
                    </h1>
                    <p style="color: #666; margin-bottom: 16px">
                        Xem lại tất cả các giao dịch đổi thẻ cào của bạn
                    </p>
                    
                    <!-- Statistics -->
                    <a-row :gutter="[16, 16]" style="margin-bottom: 16px">
                        <a-col :xs="12" :sm="8" :md="6">
                            <a-card size="small">
                                <a-statistic 
                                    title="Tổng giao dịch" 
                                    :value="totalTransactions"
                                    :value-style="{ color: '#1890ff' }"
                                />
                            </a-card>
                        </a-col>
                        <a-col :xs="12" :sm="8" :md="6">
                            <a-card size="small">
                                <a-statistic 
                                    title="Tổng tiền nhận được" 
                                    :value="totalSuccess"
                                    :formatter="(value) => formatCurrency(value as number)"
                                    :value-style="{ color: '#52c41a' }"
                                />
                            </a-card>
                        </a-col>
                    </a-row>
                    
                    <a-button type="primary" @click="$inertia.visit(route('card-exchange.index'))">
                        <SwapOutlined />
                        Đổi thẻ mới
                    </a-button>
                </div>

                <!-- Filters -->
                <a-card title="Bộ lọc" style="margin-bottom: 24px">
                    <template #extra>
                        <FilterOutlined />
                    </template>
                    
                    <a-form layout="inline" @submit.prevent="applyFilters">
                        <a-form-item label="Trạng thái">
                            <a-select 
                                v-model:value="statusFilter" 
                                placeholder="Tất cả trạng thái"
                                style="width: 150px"
                                allow-clear
                            >
                                <a-select-option value="pending">Chờ xử lý</a-select-option>
                                <a-select-option value="processing">Đang xử lý</a-select-option>
                                <a-select-option value="success">Thành công</a-select-option>
                                <a-select-option value="failed">Thất bại</a-select-option>
                                <a-select-option value="invalid_card">Thẻ không hợp lệ</a-select-option>
                            </a-select>
                        </a-form-item>
                        
                        <a-form-item label="Từ ngày">
                            <a-date-picker 
                                v-model:value="fromDate" 
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                                placeholder="Chọn ngày bắt đầu"
                            />
                        </a-form-item>
                        
                        <a-form-item label="Đến ngày">
                            <a-date-picker 
                                v-model:value="toDate" 
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                                placeholder="Chọn ngày kết thúc"
                            />
                        </a-form-item>
                        
                        <a-form-item>
                            <a-space>
                                <a-button type="primary" html-type="submit">
                                    Lọc
                                </a-button>
                                <a-button @click="clearFilters">
                                    Xóa bộ lọc
                                </a-button>
                            </a-space>
                        </a-form-item>
                    </a-form>
                </a-card>

                <!-- Transactions Table -->
                <a-card title="Danh sách giao dịch">
                    <a-table 
                        :columns="columns" 
                        :data-source="transactions.data" 
                        :pagination="{
                            current: transactions.current_page,
                            total: transactions.total,
                            pageSize: transactions.per_page,
                            showSizeChanger: false,
                            showQuickJumper: true,
                            showTotal: (total, range) => `${range[0]}-${range[1]} của ${total} giao dịch`,
                        }"
                        :scroll="{ x: 800 }"
                        row-key="id"
                    >
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.key === 'card_type'">
                                <a-space>
                                    <img 
                                        :src="record.card_type.image_url" 
                                        :alt="record.card_type.name"
                                        style="width: 20px; height: 15px; object-fit: contain"
                                    />
                                    <span>{{ record.card_type.name }}</span>
                                </a-space>
                            </template>
                            
                            <template v-else-if="column.key === 'denomination'">
                                {{ formatCurrency(record.denomination) }}
                            </template>
                            
                            <template v-else-if="column.key === 'exchange_rate'">
                                {{ record.exchange_rate }}%
                            </template>
                            
                            <template v-else-if="column.key === 'credited_amount'">
                                <span v-if="record.credited_amount" style="color: #52c41a; font-weight: 500">
                                    {{ formatCurrency(record.credited_amount) }}
                                </span>
                                <span v-else style="color: #999">-</span>
                            </template>
                            
                            <template v-else-if="column.key === 'status'">
                                <a-tag :color="getStatusTag(record.status).color">
                                    <component :is="getStatusTag(record.status).icon" style="margin-right: 4px" />
                                    {{ getStatusTag(record.status).text }}
                                </a-tag>
                            </template>
                            
                            <template v-else-if="column.key === 'created_at'">
                                <div>
                                    <div>{{ formatDate(record.created_at) }}</div>
                                    <div style="font-size: 12px; color: #999">
                                        {{ formatDateTime(record.created_at).split(' ')[1] }}
                                    </div>
                                </div>
                            </template>
                            
                            <template v-else-if="column.key === 'action'">
                                <a-button 
                                    type="link" 
                                    size="small" 
                                    @click="viewTransaction(record.id)"
                                >
                                    <EyeOutlined />
                                    Xem
                                </a-button>
                            </template>
                        </template>
                    </a-table>
                </a-card>
            </div>
        </div>
    </AppLayout>
</template>
