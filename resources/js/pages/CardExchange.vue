<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { CreditCardOutlined, HistoryOutlined, MailOutlined, SwapOutlined, WalletOutlined } from '@ant-design/icons-vue';
import { router } from '@inertiajs/vue3';
import { message } from 'ant-design-vue';
import { computed, ref, watch } from 'vue';

interface CardDenomination {
    id: number;
    value: number;
    exchange_rate: number;
    credited_amount: number;
}

interface CardType {
    id: number;
    slug: string;
    name: string;
    image_url: string;
    denominations: CardDenomination[];
}

interface User {
    wallet_balance: number;
}

interface Props {
    cardTypes: CardType[];
    user: User;
}

const props = defineProps<Props>();

const selectedCard = ref<number | null>(null);
const selectedDenomination = ref<number | null>(null);
const serial = ref('');
const pin = ref('');
const isSubmitting = ref(false);

const selectedCardInfo = computed(() => {
    return props.cardTypes.find((card: CardType) => card.id === selectedCard.value);
});

const selectedDenominationInfo = computed(() => {
    if (!selectedCardInfo.value || !selectedDenomination.value) return null;
    return selectedCardInfo.value.denominations.find((denom: CardDenomination) => denom.id === selectedDenomination.value);
});

const denominationOptions = computed(() => {
    if (!selectedCardInfo.value) return [];
    return selectedCardInfo.value.denominations.map((denom: CardDenomination) => ({
        value: denom.id,
        label: `${formatCurrency(denom.value)} - Chiết khấu ${denom.exchange_rate}%`,
        denomination: denom,
    }));
});

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN').format(amount) + ' đ';
};

watch(selectedCard, () => {
    selectedDenomination.value = null;
});

const handleExchange = () => {
    if (!selectedCard.value || !selectedDenomination.value || !serial.value || !pin.value) {
        message.error('Vui lòng điền đầy đủ thông tin');
        return;
    }

    if (serial.value.length < 10 || pin.value.length < 10) {
        message.error('Serial và mã thẻ phải có ít nhất 10 ký tự');
        return;
    }

    isSubmitting.value = true;

    router.post(
        route('card-exchange.store'),
        {
            card_type_id: selectedCard.value,
            card_denomination_id: selectedDenomination.value,
            serial: serial.value,
            pin: pin.value,
        },
        {
            onSuccess: () => {
                message.success('Thẻ cào đã được gửi xử lý thành công!');
            },
            onError: (errors) => {
                console.error('Exchange error:', errors);
                if (errors.general) {
                    message.error(errors.general);
                } else {
                    message.error('Có lỗi xảy ra khi xử lý thẻ cào');
                }
            },
            onFinish: () => {
                isSubmitting.value = false;
            },
        },
    );
};
</script>

<template>
    <AppLayout>
        <div style="background: #f5f5f5; min-height: 100vh; padding: 24px 0">
            <div style="max-width: 1200px; margin: 0 auto; padding: 0 16px">
                <a-flex justify="space-between" align="start" wrap="wrap" style="margin-bottom: 24px">
                    <div>
                        <h1 style="margin-bottom: 8px; color: #1890ff">
                            <SwapOutlined style="margin-right: 8px" />
                            Đổi Thẻ Cào
                        </h1>
                        <p style="color: #666; margin-bottom: 16px">Đổi thẻ cào thành tiền trong ví với tỷ lệ hấp dẫn</p>
                    </div>

                    <a-space size="large">
                        <a-button type="default" @click="router.get(route('card-exchange.history'))">
                            <HistoryOutlined />
                            Lịch sử đổi thẻ
                        </a-button>
                    </a-space>
                </a-flex>

                <a-row :gutter="[24, 0]">
                    <a-col :xs="24" :lg="16">
                        <a-card>
                            <a-radio-group v-model:value="selectedCard" style="width: 100%; margin-bottom: 16px">
                                <a-row :gutter="[16, 16]">
                                    <a-col :xs="12" :sm="6" v-for="card in props.cardTypes" :key="card.id">
                                        <a-radio-button
                                            :value="card.id"
                                            style="width: 100%; height: 60px; padding: 0; position: relative; overflow: hidden"
                                        >
                                            <div
                                                style="
                                                    display: flex;
                                                    flex-direction: column;
                                                    align-items: center;
                                                    justify-content: center;
                                                    height: 100%;
                                                    padding: 12px;
                                                    text-align: center;
                                                "
                                            >
                                                <img
                                                    :src="card.image_url"
                                                    :alt="card.name"
                                                    style="width: 100%; height: 100%; object-fit: contain"
                                                    @error="(e) => ((e.target as HTMLImageElement).style.display = 'none')"
                                                />
                                            </div>
                                        </a-radio-button>
                                    </a-col>
                                </a-row>
                            </a-radio-group>

                            <a-form layout="vertical">
                                <a-row :gutter="[16, 0]">
                                    <a-col :xs="24">
                                        <a-form-item
                                            label="Chọn Mệnh Giá"
                                            required
                                            :help="
                                                selectedDenominationInfo
                                                    ? `Chiết khếu ${selectedDenominationInfo.exchange_rate}% khi nạp mệnh giá ${formatCurrency(
                                                          selectedDenominationInfo.value,
                                                      )} thực nhận vào tài khoản là ${formatCurrency(selectedDenominationInfo.credited_amount)}`
                                                    : ''
                                            "
                                        >
                                            <a-select
                                                v-model:value="selectedDenomination"
                                                size="large"
                                                placeholder="Chọn mệnh giá thẻ cào"
                                                :disabled="!selectedCard"
                                                :options="denominationOptions"
                                                show-search
                                                :filter-option="false"
                                            >
                                                <template #suffixIcon>
                                                    <WalletOutlined />
                                                </template>
                                            </a-select>
                                        </a-form-item>
                                    </a-col>

                                    <a-col :xs="24" :sm="12">
                                        <a-form-item label="Số Serial" required help="Số serial trên thẻ cào (10-20 ký tự)">
                                            <a-input v-model:value="serial" placeholder="Nhập số serial" size="large" :maxlength="20" show-count>
                                                <template #prefix>
                                                    <CreditCardOutlined style="color: #bfbfbf" />
                                                </template>
                                            </a-input>
                                        </a-form-item>
                                    </a-col>

                                    <a-col :xs="24" :sm="12">
                                        <a-form-item label="Mã thẻ (PIN)" required help="Mã PIN trên thẻ cào (10-20 ký tự)">
                                            <a-input v-model:value="pin" placeholder="Nhập mã PIN" size="large" :maxlength="20" show-count>
                                                <template #prefix>
                                                    <MailOutlined style="color: #bfbfbf" />
                                                </template>
                                            </a-input>
                                        </a-form-item>
                                    </a-col>
                                </a-row>

                                <div class="mobile-submit" style="margin-top: 16px">
                                    <a-button
                                        type="primary"
                                        size="large"
                                        block
                                        style="height: 48px; font-size: 16px; font-weight: 600"
                                        :disabled="!selectedCard || !selectedDenomination || !serial || !pin || isSubmitting"
                                        :loading="isSubmitting"
                                        @click="handleExchange"
                                    >
                                        <template #icon>
                                            <SwapOutlined />
                                        </template>
                                        Đổi Thẻ Ngay
                                    </a-button>
                                </div>
                            </a-form>
                        </a-card>
                    </a-col>

                    <a-col :xs="24" :lg="8">
                        <a-affix :offset-top="24">
                            <a-card title="Xác Nhận Đổi Thẻ" style="position: sticky; top: 24px" class="desktop-summary">
                                <template #extra>
                                    <SwapOutlined />
                                </template>

                                <div v-if="!selectedCard" style="text-align: center; padding: 20px 0">
                                    <a-empty description="Chưa chọn loại thẻ">
                                        <template #image>
                                            <CreditCardOutlined style="font-size: 48px; color: #bfbfbf" />
                                        </template>
                                    </a-empty>
                                </div>

                                <div v-else>
                                    <a-descriptions :column="1" size="small" style="margin-bottom: 16px">
                                        <a-descriptions-item label="Loại thẻ">
                                            <a-space>
                                                <img
                                                    :src="selectedCardInfo?.image_url"
                                                    :alt="selectedCardInfo?.name"
                                                    style="width: 20px; height: 15px; object-fit: contain"
                                                />
                                                {{ selectedCardInfo?.name }}
                                            </a-space>
                                        </a-descriptions-item>

                                        <a-descriptions-item v-if="selectedDenominationInfo" label="Mệnh giá thẻ">
                                            {{ formatCurrency(selectedDenominationInfo.value) }}
                                        </a-descriptions-item>

                                        <a-descriptions-item v-if="selectedDenominationInfo" label="Tỷ lệ đổi">
                                            {{ selectedDenominationInfo.exchange_rate }}%
                                        </a-descriptions-item>

                                        <a-descriptions-item v-if="selectedDenominationInfo" label="Số tiền nhận được">
                                            <span style="color: #52c41a; font-weight: 600; font-size: 16px">
                                                {{ formatCurrency(selectedDenominationInfo.credited_amount) }}
                                            </span>
                                        </a-descriptions-item>
                                    </a-descriptions>

                                    <a-alert
                                        message="Lưu ý quan trọng"
                                        description="Vui lòng kiểm tra kỹ thông tin thẻ cào trước khi gửi. Thẻ sai thông tin hoặc đã sử dụng sẽ không được hoàn tiền."
                                        type="warning"
                                        show-icon
                                        style="margin-bottom: 16px"
                                    />

                                    <a-button
                                        type="primary"
                                        size="large"
                                        block
                                        style="margin-top: 20px; height: 48px; font-size: 16px; font-weight: 600"
                                        :disabled="!selectedCard || !selectedDenomination || !serial || !pin || isSubmitting"
                                        :loading="isSubmitting"
                                        @click="handleExchange"
                                    >
                                        <template #icon>
                                            <SwapOutlined />
                                        </template>
                                        Đổi Thẻ Ngay
                                    </a-button>
                                </div>
                            </a-card>
                        </a-affix>
                    </a-col>
                </a-row>
            </div>
        </div>
    </AppLayout>
</template>

<style scoped>
@media (min-width: 992px) {
    .mobile-submit {
        display: none;
    }
}

@media (max-width: 991px) {
    .desktop-summary {
        display: none;
    }
}
</style>
