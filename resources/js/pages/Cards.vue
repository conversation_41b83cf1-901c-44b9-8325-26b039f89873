<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { CreditCardOutlined, MailOutlined, ShoppingCartOutlined } from '@ant-design/icons-vue';
import { computed, ref, watch } from 'vue';

const selectedCard = ref('');
const selectedValue = ref('');
const email = ref('');
const quantity = ref(1);

const cardTypes = [
    {
        id: 'garena',
        name: 'Thẻ Garena',
        image: 'https://napsieutoc.com/files/the-cao/the-garena.png',
        discount: '3%',
        values: [10000, 20000, 50000, 100000, 200000, 500000],
    },
    {
        id: 'zing',
        name: 'Thẻ Z<PERSON>',
        image: 'https://napsieutoc.com/files/the-cao/the-zing.png',
        discount: '4%',
        values: [10000, 20000, 50000, 100000, 200000, 500000],
    },
    {
        id: 'vcoin',
        name: 'Thẻ <PERSON>oin',
        image: 'https://napsieutoc.com/files/the-cao/the-vcoin.png',
        discount: '5%',
        values: [10000, 20000, 50000, 100000, 200000, 500000, 1000000, 2000000],
    },
    {
        id: 'viettel',
        name: 'Thẻ Viettel',
        image: 'https://napsieutoc.com/files/the-cao/the-viettel.png',
        discount: '2%',
        values: [10000, 20000, 50000, 100000, 200000, 500000],
    },
    {
        id: 'vinaphone',
        name: 'Thẻ Vinaphone',
        image: 'https://napsieutoc.com/files/the-cao/the-vinaphone.png',
        discount: '3%',
        values: [10000, 20000, 50000, 100000, 200000, 500000],
    },
    {
        id: 'mobifone',
        name: 'Thẻ Mobifone',
        image: 'https://napsieutoc.com/files/the-cao/the-mobifone.png',
        discount: '3%',
        values: [10000, 20000, 50000, 100000, 200000, 500000],
    },
    {
        id: 'appota',
        name: 'Thẻ Appota',
        image: 'https://napsieutoc.com/files/the-cao/the-appota.png',
        discount: '4%',
        values: [10000, 20000, 50000, 100000, 200000, 500000],
    },
    {
        id: 'funcard',
        name: 'Thẻ Funcard',
        image: 'https://napsieutoc.com/files/the-cao/the-funcard.png',
        discount: '4%',
        values: [10000, 20000, 50000, 100000, 200000, 500000],
    },
];

const selectedCardInfo = computed(() => {
    return cardTypes.find((card) => card.id === selectedCard.value);
});

const totalAmount = computed(() => {
    if (!selectedValue.value) return 0;
    return parseInt(selectedValue.value) * quantity.value;
});

const discountAmount = computed(() => {
    if (!selectedCardInfo.value || !totalAmount.value || !selectedCardInfo.value.discount) return 0;
    const discountPercent = parseInt(selectedCardInfo.value.discount) / 100;
    return totalAmount.value * discountPercent;
});

const finalAmount = computed(() => {
    return totalAmount.value - discountAmount.value;
});

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN').format(amount) + ' đ';
};

const getDiscountedPrice = (value: number) => {
    if (!selectedCardInfo.value || !selectedCardInfo.value.discount) return value;
    const discountPercent = parseInt(selectedCardInfo.value.discount) / 100;
    return value - value * discountPercent;
};

watch(selectedCard, () => {
    selectedValue.value = '';
});

const handlePurchase = () => {
    if (!selectedCard.value || !selectedValue.value || !email.value) {
        return;
    }

    console.log('Purchase:', {
        card: selectedCard.value,
        value: selectedValue.value,
        quantity: quantity.value,
        email: email.value,
        total: finalAmount.value,
    });
};
</script>

<template>
    <AppLayout>
        <div style="background: #f5f5f5; min-height: 100vh; padding: 24px 0">
            <div style="max-width: 1200px; margin: 0 auto; padding: 0 16px">
                <a-row :gutter="[24, 0]">
                    <a-col :xs="24" :lg="16">
                        <a-card title="Chọn Loại Thẻ Cào" style="margin-bottom: 24px">
                            <template #extra>
                                <a-tag color="processing">{{ cardTypes.length }} loại thẻ</a-tag>
                            </template>

                            <a-radio-group v-model:value="selectedCard" style="width: 100%">
                                <a-row :gutter="[16, 16]">
                                    <a-col :xs="12" :sm="6" v-for="card in cardTypes" :key="card.id">
                                        <a-radio-button
                                            :value="card.id"
                                            style="width: 100%; height: 60px; padding: 0; position: relative; overflow: hidden"
                                        >
                                            <div
                                                style="
                                                    display: flex;
                                                    flex-direction: column;
                                                    align-items: center;
                                                    justify-content: center;
                                                    height: 100%;
                                                    padding: 12px;
                                                    text-align: center;
                                                "
                                            >
                                                <img
                                                    :src="card.image"
                                                    :alt="card.name"
                                                    style="width: 100%; height: 100%; object-fit: contain"
                                                    @error="(e) => ((e.target as HTMLImageElement).style.display = 'none')"
                                                />
                                            </div>
                                        </a-radio-button>
                                    </a-col>
                                </a-row>
                            </a-radio-group>
                        </a-card>

                        <a-card title="Chọn Mệnh Giá" style="margin-bottom: 24px">
                            <template #extra v-if="selectedCard">
                                <a-tag color="blue">{{ selectedCardInfo?.name }}</a-tag>
                            </template>

                            <a-radio-group v-if="selectedCard" v-model:value="selectedValue" style="width: 100%">
                                <a-row :gutter="[12, 12]">
                                    <a-col :xs="12" :sm="8" :md="6" v-for="value in selectedCardInfo?.values" :key="value">
                                        <a-radio-button
                                            :value="value.toString()"
                                            style="
                                                display: flex;
                                                flex-direction: column;
                                                align-items: center;
                                                justify-content: center;
                                                width: 100%;
                                                text-align: center;
                                                height: 56px;
                                                padding: 8px;
                                            "
                                        >
                                            <div style="font-weight: 500; line-height: 1.2">
                                                {{ formatCurrency(value) }}
                                            </div>
                                            <div style="line-height: 1; margin-top: 4px">
                                                <span style="font-size: 12px; margin-top: 2px; color: #999"> Giá bán: </span>
                                                <span style="color: #52c41a; font-size: 12px; margin-top: 2px">
                                                    {{ formatCurrency(getDiscountedPrice(value)) }}
                                                </span>
                                            </div>
                                        </a-radio-button>
                                    </a-col>
                                </a-row>
                            </a-radio-group>
                            <div v-else>
                                <a-empty description="Chưa chọn loại thẻ" />
                            </div>
                        </a-card>

                        <a-card title="Thông Tin Khách Hàng" style="margin-bottom: 24px">
                            <a-form layout="vertical">
                                <a-row :gutter="[16, 16]">
                                    <a-col :xs="24" :sm="16">
                                        <a-form-item label="Email nhận thẻ" required help="Mã thẻ sẽ được gửi về email này">
                                            <a-input v-model:value="email" placeholder="Nhập email để nhận mã thẻ" size="large" type="email">
                                                <template #prefix>
                                                    <MailOutlined style="color: #bfbfbf" />
                                                </template>
                                            </a-input>
                                        </a-form-item>
                                    </a-col>

                                    <a-col :xs="24" :sm="8">
                                        <a-form-item label="Số lượng">
                                            <a-input-number v-model:value="quantity" :min="1" :max="10" size="large" style="width: 100%" />
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                            </a-form>
                        </a-card>
                    </a-col>

                    <a-col :xs="24" :lg="8">
                        <a-affix :offset-top="24">
                            <a-card title="Tóm Tắt Đơn Hàng" style="position: sticky; top: 24px">
                                <template #extra>
                                    <ShoppingCartOutlined />
                                </template>

                                <div v-if="!selectedCard" style="text-align: center; padding: 20px 0">
                                    <a-empty description="Chưa chọn loại thẻ">
                                        <template #image>
                                            <CreditCardOutlined style="font-size: 48px; color: #bfbfbf" />
                                        </template>
                                    </a-empty>
                                </div>

                                <div v-else>
                                    <a-descriptions :column="1" size="small" style="margin-bottom: 16px">
                                        <a-descriptions-item label="Loại thẻ">
                                            <a-space>
                                                <img
                                                    :src="selectedCardInfo?.image"
                                                    :alt="selectedCardInfo?.name"
                                                    style="width: 20px; height: 15px; object-fit: contain"
                                                />
                                                {{ selectedCardInfo?.name }}
                                            </a-space>
                                        </a-descriptions-item>

                                        <a-descriptions-item v-if="selectedValue" label="Mệnh giá">
                                            {{ formatCurrency(parseInt(selectedValue)) }}
                                        </a-descriptions-item>

                                        <a-descriptions-item v-if="selectedValue" label="Số lượng"> {{ quantity }} thẻ </a-descriptions-item>

                                        <a-descriptions-item v-if="email" label="Email">
                                            {{ email }}
                                        </a-descriptions-item>
                                    </a-descriptions>

                                    <div style="margin-top: 16px">
                                        <a-divider style="margin: 16px 0" />

                                        <a-space direction="vertical" style="width: 100%" :size="8">
                                            <div style="display: flex; justify-content: space-between">
                                                <span>Tạm tính:</span>
                                                <span style="font-weight: 500">{{ formatCurrency(totalAmount) }}</span>
                                            </div>

                                            <div style="display: flex; justify-content: space-between">
                                                <span style="color: #52c41a"> Chiết khấu ({{ selectedCardInfo?.discount }}): </span>
                                                <span style="color: #52c41a; font-weight: 500"> -{{ formatCurrency(discountAmount) }} </span>
                                            </div>

                                            <a-divider style="margin: 8px 0" />

                                            <div style="display: flex; justify-content: space-between; font-size: 16px">
                                                <span style="font-weight: 600">Tổng cần thanh toán:</span>
                                                <span style="color: #1890ff; font-weight: 600; font-size: 18px">
                                                    {{ formatCurrency(finalAmount) }}
                                                </span>
                                            </div>
                                        </a-space>

                                        <a-button
                                            type="primary"
                                            size="large"
                                            block
                                            style="margin-top: 20px; height: 48px; font-size: 16px; font-weight: 600"
                                            :disabled="!email || !selectedValue"
                                            @click="handlePurchase"
                                        >
                                            <template #icon>
                                                <CreditCardOutlined />
                                            </template>
                                            Mua Ngay
                                        </a-button>
                                    </div>
                                </div>
                            </a-card>
                        </a-affix>
                    </a-col>
                </a-row>
            </div>
        </div>
    </AppLayout>
</template>
