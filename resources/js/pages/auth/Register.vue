<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { LockOutlined, MailOutlined, UserAddOutlined, UserOutlined } from '@ant-design/icons-vue';
import { useForm } from '@inertiajs/vue3';
import { message } from 'ant-design-vue';
import { computed } from 'vue';

const form = useForm({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
});

const submit = () => {
    form.post(route('register'), {
        onFinish: () => {
            form.reset('password', 'password_confirmation');
        },
        onSuccess: () => {
            message.success('Đăng ký thành công! Chào mừng bạn đến với hệ thống.');
        },
    });
};

const hasErrors = computed(() => {
    return Object.keys(form.errors).length > 0;
});

const passwordsMatch = computed(() => {
    if (!form.password || !form.password_confirmation) return true;
    return form.password === form.password_confirmation;
});
</script>

<template>
    <AppLayout>
        <a-row justify="center" align="middle" class="min-h-screen">
            <a-col :xs="22" :sm="16" :md="12" :lg="8" :xl="6">
                <a-card class="auth-card" :bordered="false">
                    <div class="auth-header">
                        <h1 class="auth-title">Đăng Ký</h1>
                        <p class="auth-subtitle">Tạo tài khoản mới để bắt đầu</p>
                    </div>

                    <a-form layout="vertical" @submit.prevent="submit" :model="form" class="auth-form">
                        <a-form-item label="Họ và tên" :validate-status="form.errors.name ? 'error' : ''" :help="form.errors.name">
                            <a-input v-model:value="form.name" size="large" placeholder="Nhập họ và tên của bạn" :disabled="form.processing">
                                <template #prefix>
                                    <UserOutlined class="text-gray-400" />
                                </template>
                            </a-input>
                        </a-form-item>

                        <a-form-item label="Email" :validate-status="form.errors.email ? 'error' : ''" :help="form.errors.email">
                            <a-input
                                v-model:value="form.email"
                                type="email"
                                size="large"
                                placeholder="Nhập email của bạn"
                                :disabled="form.processing"
                            >
                                <template #prefix>
                                    <MailOutlined class="text-gray-400" />
                                </template>
                            </a-input>
                        </a-form-item>

                        <a-form-item label="Mật khẩu" :validate-status="form.errors.password ? 'error' : ''" :help="form.errors.password">
                            <a-input-password
                                v-model:value="form.password"
                                size="large"
                                placeholder="Nhập mật khẩu (tối thiểu 8 ký tự)"
                                :disabled="form.processing"
                            >
                                <template #prefix>
                                    <LockOutlined class="text-gray-400" />
                                </template>
                            </a-input-password>
                        </a-form-item>

                        <a-form-item
                            label="Xác nhận mật khẩu"
                            :validate-status="form.errors.password_confirmation ? 'error' : !passwordsMatch ? 'error' : ''"
                            :help="form.errors.password_confirmation || (!passwordsMatch ? 'Mật khẩu xác nhận không khớp' : '')"
                        >
                            <a-input-password
                                v-model:value="form.password_confirmation"
                                size="large"
                                placeholder="Nhập lại mật khẩu"
                                :disabled="form.processing"
                            >
                                <template #prefix>
                                    <LockOutlined class="text-gray-400" />
                                </template>
                            </a-input-password>
                        </a-form-item>

                        <a-form-item class="mb-4">
                            <a-button
                                type="primary"
                                html-type="submit"
                                size="large"
                                block
                                :loading="form.processing"
                                :disabled="!passwordsMatch"
                                class="auth-submit-btn"
                            >
                                <template #icon>
                                    <UserAddOutlined />
                                </template>
                                Đăng Ký
                            </a-button>
                        </a-form-item>

                        <div class="auth-footer">
                            <span class="text-gray-600">Đã có tài khoản? </span>
                            <a-button type="link" @click="$inertia.visit(route('login'))" class="p-0 font-medium"> Đăng nhập ngay </a-button>
                        </div>
                    </a-form>
                </a-card>
            </a-col>
        </a-row>
    </AppLayout>
</template>

<style scoped>
.auth-card {
    box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-radius: 16px;
    overflow: hidden;
}

.auth-header {
    text-align: center;
    margin-bottom: 32px;
}

.auth-title {
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 8px;
}

.auth-subtitle {
    color: #6b7280;
    font-size: 16px;
    margin: 0;
}

.auth-form {
    margin-top: 24px;
}

.auth-submit-btn {
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
}

.auth-footer {
    text-align: center;
    margin-top: 16px;
}

.min-h-screen {
    min-height: calc(100ch - 150px);
}

.mb-6 {
    margin-bottom: 24px;
}

.mb-4 {
    margin-bottom: 16px;
}
</style>
