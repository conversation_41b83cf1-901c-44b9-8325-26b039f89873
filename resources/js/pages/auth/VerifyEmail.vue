<script setup lang="ts">
import { LogoutOutlined, MailOutlined, SendOutlined } from '@ant-design/icons-vue';
import { useForm } from '@inertiajs/vue3';
import { message } from 'ant-design-vue';
import { ref } from 'vue';

interface Props {
    status?: string;
}

const props = withDefaults(defineProps<Props>(), {
    status: '',
});

const form = useForm({});
const resendCooldown = ref(0);
const cooldownInterval = ref<NodeJS.Timeout | null>(null);

const resendVerification = () => {
    form.post(route('verification.send'), {
        onSuccess: () => {
            message.success('Email xác thực đã được gửi lại!');
            startCooldown();
        },
        onError: () => {
            message.error('Có lỗi xảy ra khi gửi email');
        },
    });
};

const startCooldown = () => {
    resendCooldown.value = 60;
    cooldownInterval.value = setInterval(() => {
        resendCooldown.value--;
        if (resendCooldown.value <= 0) {
            clearInterval(cooldownInterval.value!);
            cooldownInterval.value = null;
        }
    }, 1000);
};

const logout = () => {
    form.post(route('logout'), {
        onSuccess: () => {
            message.info('Đã đăng xuất thành công');
        },
    });
};
</script>

<template>
    <div class="auth-container">
        <a-row justify="center" align="middle" class="min-h-screen">
            <a-col :xs="22" :sm="16" :md="12" :lg="8" :xl="6">
                <a-card class="auth-card" :bordered="false">
                    <!-- Header -->
                    <div class="auth-header">
                        <div class="verification-icon">
                            <MailOutlined />
                        </div>
                        <h1 class="auth-title">Xác Thực Email</h1>
                        <p class="auth-subtitle">
                            Chúng tôi đã gửi liên kết xác thực đến email của bạn. Vui lòng kiểm tra hộp thư và nhấp vào liên kết để xác thực tài
                            khoản.
                        </p>
                    </div>

                    <!-- Status Message -->
                    <a-alert
                        v-if="status === 'verification-link-sent'"
                        message="Liên kết xác thực mới đã được gửi đến email của bạn!"
                        type="success"
                        show-icon
                        class="mb-6"
                    />

                    <!-- Instructions -->
                    <div class="verification-steps">
                        <h3 class="steps-title">Hướng dẫn xác thực:</h3>
                        <ol class="steps-list">
                            <li>Kiểm tra hộp thư email của bạn</li>
                            <li>Tìm email từ hệ thống (kiểm tra cả thư spam)</li>
                            <li>Nhấp vào liên kết "Xác thực email"</li>
                            <li>Quay lại trang web để tiếp tục</li>
                        </ol>
                    </div>

                    <!-- Actions -->
                    <div class="verification-actions">
                        <a-button
                            type="primary"
                            size="large"
                            block
                            :loading="form.processing"
                            :disabled="resendCooldown > 0"
                            @click="resendVerification"
                            class="resend-btn mb-4"
                        >
                            <template #icon>
                                <SendOutlined />
                            </template>
                            <span v-if="resendCooldown > 0"> Gửi lại sau {{ resendCooldown }}s </span>
                            <span v-else> Gửi Lại Email Xác Thực </span>
                        </a-button>

                        <a-button type="default" size="large" block @click="logout" class="logout-btn">
                            <template #icon>
                                <LogoutOutlined />
                            </template>
                            Đăng Xuất
                        </a-button>
                    </div>

                    <!-- Help Info -->
                    <a-alert message="Không nhận được email?" type="info" show-icon class="mt-6">
                        <template #description>
                            <ul class="help-list">
                                <li>Kiểm tra thư mục spam/junk</li>
                                <li>Đảm bảo email chính xác</li>
                                <li>Thử gửi lại email xác thực</li>
                                <li>Liên hệ hỗ trợ nếu vẫn gặp vấn đề</li>
                            </ul>
                        </template>
                    </a-alert>
                </a-card>
            </a-col>
        </a-row>
    </div>
</template>

<style scoped>
.auth-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px 0;
}

.auth-card {
    box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-radius: 16px;
    overflow: hidden;
}

.auth-header {
    text-align: center;
    margin-bottom: 32px;
}

.verification-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    font-size: 32px;
    color: white;
}

.auth-title {
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 16px;
}

.auth-subtitle {
    color: #6b7280;
    font-size: 16px;
    margin: 0;
    line-height: 1.6;
}

.verification-steps {
    margin: 24px 0;
    padding: 20px;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.steps-title {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 12px;
}

.steps-list {
    margin: 0;
    padding-left: 20px;
    color: #6b7280;
}

.steps-list li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.verification-actions {
    margin-top: 24px;
}

.resend-btn,
.logout-btn {
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
}

.help-list {
    margin: 8px 0 0 0;
    padding-left: 20px;
}

.help-list li {
    margin-bottom: 4px;
    color: #6b7280;
}

.min-h-screen {
    min-height: 100vh;
}

.mb-6 {
    margin-bottom: 24px;
}

.mb-4 {
    margin-bottom: 16px;
}

.mt-6 {
    margin-top: 24px;
}
</style>
