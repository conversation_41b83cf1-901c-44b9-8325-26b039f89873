<script setup lang="ts">
import { router, useForm } from '@inertiajs/vue3';
import { MailOutlined, LockOutlined, CheckOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { computed } from 'vue';
import AppLayout from '@/layouts/AppLayout.vue';

interface Props {
    email: string;
    token: string;
}

const props = defineProps<Props>();

const form = useForm({
    token: props.token,
    email: props.email,
    password: '',
    password_confirmation: '',
});

const submit = () => {
    form.post(route('password.store'), {
        onFinish: () => {
            form.reset('password', 'password_confirmation');
        },
        onSuccess: () => {
            message.success('Mật khẩu đã được đặt lại thành công! Bạn có thể đăng nhập ngay.');
        },
    });
};

const hasErrors = computed(() => {
    return Object.keys(form.errors).length > 0;
});

const passwordsMatch = computed(() => {
    if (!form.password || !form.password_confirmation) return true;
    return form.password === form.password_confirmation;
});

const passwordStrength = computed(() => {
    const password = form.password;
    if (!password) return { level: 0, text: '', color: '' };
    
    let score = 0;
    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;
    
    if (score < 2) return { level: 1, text: 'Yếu', color: 'error' };
    if (score < 4) return { level: 2, text: 'Trung bình', color: 'warning' };
    return { level: 3, text: 'Mạnh', color: 'success' };
});
</script>

<template>
    <AppLayout>
        <a-row justify="center" align="middle" class="min-h-screen">
            <a-col :xs="22" :sm="16" :md="12" :lg="8" :xl="6">
                <a-card class="auth-card" :bordered="false">
                    <div class="auth-header">
                        <h1 class="auth-title">Đặt Lại Mật Khẩu</h1>
                        <p class="auth-subtitle">
                            Tạo mật khẩu mới cho tài khoản của bạn
                        </p>
                    </div>

                    <a-alert
                        v-if="hasErrors"
                        message="Vui lòng kiểm tra lại thông tin"
                        type="error"
                        show-icon
                        class="mb-6"
                    />

                    <a-form
                        layout="vertical"
                        @submit.prevent="submit"
                        :model="form"
                        class="auth-form"
                    >
                        <input type="hidden" v-model="form.token" />

                        <a-form-item
                            label="Email"
                            :validate-status="form.errors.email ? 'error' : ''"
                            :help="form.errors.email"
                        >
                            <a-input
                                v-model:value="form.email"
                                type="email"
                                size="large"
                                disabled
                                class="disabled-input"
                            >
                                <template #prefix>
                                    <MailOutlined class="text-gray-400" />
                                </template>
                            </a-input>
                        </a-form-item>

                        <a-form-item
                            label="Mật khẩu mới"
                            :validate-status="form.errors.password ? 'error' : ''"
                            :help="form.errors.password"
                        >
                            <a-input-password
                                v-model:value="form.password"
                                size="large"
                                placeholder="Nhập mật khẩu mới (tối thiểu 8 ký tự)"
                                :disabled="form.processing"
                            >
                                <template #prefix>
                                    <LockOutlined class="text-gray-400" />
                                </template>
                            </a-input-password>
                            
                            <div v-if="form.password" class="password-strength mt-2">
                                <div class="flex items-center gap-2">
                                    <span class="text-sm text-gray-600">Độ mạnh:</span>
                                    <a-tag :color="passwordStrength.color" size="small">
                                        {{ passwordStrength.text }}
                                    </a-tag>
                                </div>
                            </div>
                        </a-form-item>

                        <a-form-item
                            label="Xác nhận mật khẩu mới"
                            :validate-status="form.errors.password_confirmation ? 'error' : (!passwordsMatch ? 'error' : '')"
                            :help="form.errors.password_confirmation || (!passwordsMatch ? 'Mật khẩu xác nhận không khớp' : '')"
                        >
                            <a-input-password
                                v-model:value="form.password_confirmation"
                                size="large"
                                placeholder="Nhập lại mật khẩu mới"
                                :disabled="form.processing"
                            >
                                <template #prefix>
                                    <LockOutlined class="text-gray-400" />
                                </template>
                            </a-input-password>
                        </a-form-item>

                        <a-form-item class="mb-4">
                            <a-button
                                type="primary"
                                html-type="submit"
                                size="large"
                                block
                                :loading="form.processing"
                                :disabled="!passwordsMatch || !form.password"
                                class="auth-submit-btn"
                            >
                                <template #icon>
                                    <CheckOutlined />
                                </template>
                                Đặt Lại Mật Khẩu
                            </a-button>
                        </a-form-item>

                        <div class="auth-footer">
                            <span class="text-gray-600">Nhớ mật khẩu? </span>
                            <a-button
                                type="link"
                                @click="router.visit(route('login'))"
                                class="p-0 font-medium"
                            >
                                Đăng nhập ngay
                            </a-button>
                        </div>
                    </a-form>
                </a-card>
            </a-col>
        </a-row>
    </AppLayout>
</template>

<style scoped>
.auth-card {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-radius: 16px;
    overflow: hidden;
}

.auth-header {
    text-align: center;
    margin-bottom: 32px;
}

.auth-title {
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 8px;
}

.auth-subtitle {
    color: #6b7280;
    font-size: 16px;
    margin: 0;
    line-height: 1.5;
}

.auth-form {
    margin-top: 24px;
}

.auth-submit-btn {
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
}

.auth-footer {
    text-align: center;
    margin-top: 16px;
}

.disabled-input {
    background-color: #f5f5f5;
}

.password-strength {
    margin-top: 8px;
}

.min-h-screen {
    min-height: calc(100vh - 150px);
}

.mb-6 {
    margin-bottom: 24px;
}

.mb-4 {
    margin-bottom: 16px;
}

.mt-2 {
    margin-top: 8px;
}
</style>
