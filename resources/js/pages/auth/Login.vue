<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { LockOutlined, LoginOutlined, UserOutlined } from '@ant-design/icons-vue';
import { router, useForm } from '@inertiajs/vue3';
import { message } from 'ant-design-vue';
import { computed } from 'vue';

interface Props {
    canResetPassword?: boolean;
    status?: string;
}

const props = withDefaults(defineProps<Props>(), {
    canResetPassword: true,
    status: '',
});

const form = useForm({
    email: '',
    password: '',
    remember: false,
});

const submit = () => {
    form.post(route('login'), {
        onFinish: () => {
            form.reset('password');
        },
        onSuccess: () => {
            message.success('Đăng nhập thành công!');
        },
    });
};

const hasErrors = computed(() => {
    return Object.keys(form.errors).length > 0;
});
</script>

<template>
    <AppLayout>
        <a-row justify="center" align="middle" class="min-h-screen">
            <a-col :xs="22" :sm="16" :md="12" :lg="8" :xl="6">
                <a-card class="auth-card" :bordered="false">
                    <div class="auth-header">
                        <h1 class="auth-title">Đăng Nhập</h1>
                        <p class="auth-subtitle">Chào mừng bạn quay trở lại!</p>
                    </div>

                    <a-alert v-if="status" :message="status" type="success" show-icon class="mb-6" />

                    <a-form layout="vertical" @submit.prevent="submit" :model="form" class="auth-form">
                        <a-form-item label="Email" :validate-status="form.errors.email ? 'error' : ''" :help="form.errors.email">
                            <a-input
                                v-model:value="form.email"
                                type="email"
                                size="large"
                                placeholder="Nhập email của bạn"
                                :disabled="form.processing"
                            >
                                <template #prefix>
                                    <UserOutlined class="text-gray-400" />
                                </template>
                            </a-input>
                        </a-form-item>

                        <a-form-item label="Mật khẩu" :validate-status="form.errors.password ? 'error' : ''" :help="form.errors.password">
                            <a-input-password v-model:value="form.password" size="large" placeholder="Nhập mật khẩu" :disabled="form.processing">
                                <template #prefix>
                                    <LockOutlined class="text-gray-400" />
                                </template>
                            </a-input-password>
                        </a-form-item>

                        <a-form-item>
                            <a-flex justify="space-between">
                                <a-checkbox v-model:checked="form.remember"> Ghi nhớ đăng nhập </a-checkbox>
                                <a-button
                                    v-if="canResetPassword"
                                    type="link"
                                    size="small"
                                    @click="router.visit(route('password.request'))"
                                    class="p-0"
                                >
                                    Quên mật khẩu?
                                </a-button>
                            </a-flex>
                        </a-form-item>

                        <a-form-item class="mb-4">
                            <a-button type="primary" html-type="submit" size="large" block :loading="form.processing" class="auth-submit-btn">
                                <template #icon>
                                    <LoginOutlined />
                                </template>
                                Đăng Nhập
                            </a-button>
                        </a-form-item>

                        <div class="auth-footer">
                            <span class="text-gray-600">Chưa có tài khoản? </span>
                            <a-button type="link" @click="router.visit(route('register'))" class="p-0 font-medium"> Đăng ký ngay </a-button>
                        </div>
                    </a-form>
                </a-card>
            </a-col>
        </a-row>
    </AppLayout>
</template>

<style scoped>
.auth-card {
    box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-radius: 16px;
    overflow: hidden;
}

.auth-header {
    text-align: center;
    margin-bottom: 32px;
}

.auth-title {
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 8px;
}

.auth-subtitle {
    color: #6b7280;
    font-size: 16px;
    margin: 0;
}

.auth-form {
    margin-top: 24px;
}

.auth-submit-btn {
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
}

.auth-footer {
    text-align: center;
    margin-top: 16px;
}

.min-h-screen {
    min-height: calc(100vh - 150px);
}

.mb-6 {
    margin-bottom: 24px;
}

.mb-4 {
    margin-bottom: 16px;
}
</style>
