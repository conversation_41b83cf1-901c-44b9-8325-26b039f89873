<script setup lang="ts">
import { useForm } from '@inertiajs/vue3';
import { LockOutlined, CheckOutlined, ArrowLeftOutlined, ShieldCheckOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { computed } from 'vue';

const form = useForm({
    password: '',
});

const submit = () => {
    form.post(route('password.confirm'), {
        onFinish: () => {
            form.reset('password');
        },
        onSuccess: () => {
            message.success('Xác thực mật khẩu thành công!');
        },
        onError: () => {
            message.error('Mật khẩu không chính xác');
        }
    });
};

const hasErrors = computed(() => {
    return Object.keys(form.errors).length > 0;
});
</script>

<template>
    <div class="auth-container">
        <a-row justify="center" align="middle" class="min-h-screen">
            <a-col :xs="22" :sm="16" :md="12" :lg="8" :xl="6">
                <a-card class="auth-card" :bordered="false">
                    <!-- Header -->
                    <div class="auth-header">
                        <div class="security-icon">
                            <ShieldCheckOutlined />
                        </div>
                        <h1 class="auth-title">Xác Thực Bảo Mật</h1>
                        <p class="auth-subtitle">
                            Để bảo vệ tài khoản của bạn, vui lòng xác nhận mật khẩu trước khi tiếp tục thực hiện hành động này.
                        </p>
                    </div>

                    <!-- Error Alert -->
                    <a-alert
                        v-if="hasErrors"
                        message="Mật khẩu không chính xác"
                        description="Vui lòng kiểm tra lại mật khẩu của bạn"
                        type="error"
                        show-icon
                        class="mb-6"
                    />

                    <!-- Security Notice -->
                    <a-alert
                        message="Yêu cầu xác thực"
                        description="Đây là khu vực bảo mật. Bạn cần xác nhận mật khẩu để truy cập."
                        type="warning"
                        show-icon
                        class="mb-6"
                    />

                    <!-- Confirm Password Form -->
                    <a-form
                        layout="vertical"
                        @submit.prevent="submit"
                        :model="form"
                        class="auth-form"
                    >
                        <a-form-item
                            label="Mật khẩu hiện tại"
                            :validate-status="form.errors.password ? 'error' : ''"
                            :help="form.errors.password"
                        >
                            <a-input-password
                                v-model:value="form.password"
                                size="large"
                                placeholder="Nhập mật khẩu để xác thực"
                                :disabled="form.processing"
                                autofocus
                            >
                                <template #prefix>
                                    <LockOutlined class="text-gray-400" />
                                </template>
                            </a-input-password>
                        </a-form-item>

                        <a-form-item class="mb-4">
                            <a-button
                                type="primary"
                                html-type="submit"
                                size="large"
                                block
                                :loading="form.processing"
                                :disabled="!form.password"
                                class="auth-submit-btn"
                            >
                                <template #icon>
                                    <CheckOutlined />
                                </template>
                                Xác Thực
                            </a-button>
                        </a-form-item>

                        <!-- Cancel Action -->
                        <div class="auth-footer">
                            <a-button
                                type="link"
                                @click="window.history.back()"
                                class="p-0 font-medium"
                            >
                                <template #icon>
                                    <ArrowLeftOutlined />
                                </template>
                                Hủy bỏ
                            </a-button>
                        </div>
                    </a-form>

                    <!-- Security Tips -->
                    <div class="security-tips">
                        <h4 class="tips-title">💡 Mẹo bảo mật:</h4>
                        <ul class="tips-list">
                            <li>Không chia sẻ mật khẩu với bất kỳ ai</li>
                            <li>Sử dụng mật khẩu mạnh và duy nhất</li>
                            <li>Đăng xuất sau khi sử dụng xong</li>
                            <li>Cập nhật mật khẩu định kỳ</li>
                        </ul>
                    </div>
                </a-card>
            </a-col>
        </a-row>
    </div>
</template>

<style scoped>
.auth-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px 0;
}

.auth-card {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-radius: 16px;
    overflow: hidden;
}

.auth-header {
    text-align: center;
    margin-bottom: 32px;
}

.security-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    font-size: 32px;
    color: white;
}

.auth-title {
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 16px;
}

.auth-subtitle {
    color: #6b7280;
    font-size: 16px;
    margin: 0;
    line-height: 1.6;
}

.auth-form {
    margin-top: 24px;
}

.auth-submit-btn {
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
}

.auth-footer {
    text-align: center;
    margin-top: 16px;
}

.security-tips {
    margin-top: 24px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #f59e0b;
}

.tips-title {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 12px;
}

.tips-list {
    margin: 0;
    padding-left: 20px;
    color: #6b7280;
}

.tips-list li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.min-h-screen {
    min-height: 100vh;
}

.mb-6 {
    margin-bottom: 24px;
}

.mb-4 {
    margin-bottom: 16px;
}
</style>
