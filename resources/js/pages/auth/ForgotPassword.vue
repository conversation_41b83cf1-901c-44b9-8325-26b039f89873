<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { ArrowLeftOutlined, MailOutlined, SendOutlined } from '@ant-design/icons-vue';
import { router, useForm } from '@inertiajs/vue3';
import { message } from 'ant-design-vue';
import { computed } from 'vue';

const form = useForm({
    email: '',
});

const submit = () => {
    form.post(route('password.email'), {
        onSuccess: () => {
            form.reset();
            message.success('Liên kết đặt lại mật khẩu đã được gửi đến email của bạn!');
        },
        onError: () => {
            message.error('Có lỗi xảy ra khi gửi email');
        },
    });
};

const hasErrors = computed(() => {
    return Object.keys(form.errors).length > 0;
});
</script>

<template>
    <AppLayout>
        <a-row justify="center" align="middle" class="min-h-screen">
            <a-col :xs="22" :sm="16" :md="12" :lg="8" :xl="6">
                <a-card class="auth-card" :bordered="false">
                    <div class="auth-header">
                        <h1 class="auth-title">Quên Mật Khẩu</h1>
                        <p class="auth-subtitle">Nhập email của bạn và chúng tôi sẽ gửi liên kết đặt lại mật khẩu</p>
                    </div>

                    <a-form layout="vertical" @submit.prevent="submit" :model="form" class="auth-form">
                        <a-form-item label="Email" :validate-status="form.errors.email ? 'error' : ''" :help="form.errors.email">
                            <a-input
                                v-model:value="form.email"
                                type="email"
                                size="large"
                                placeholder="Nhập email đã đăng ký"
                                :disabled="form.processing"
                            >
                                <template #prefix>
                                    <MailOutlined class="text-gray-400" />
                                </template>
                            </a-input>
                        </a-form-item>

                        <a-form-item class="mb-4">
                            <a-button type="primary" html-type="submit" size="large" block :loading="form.processing" class="auth-submit-btn">
                                <template #icon>
                                    <SendOutlined />
                                </template>
                                Gửi Liên Kết Đặt Lại
                            </a-button>
                        </a-form-item>

                        <div class="auth-footer">
                            <a-button type="link" @click="router.visit(route('login'))" class="p-0 font-medium">
                                <template #icon>
                                    <ArrowLeftOutlined />
                                </template>
                                Quay lại đăng nhập
                            </a-button>
                        </div>
                    </a-form>
                </a-card>
            </a-col>
        </a-row>
    </AppLayout>
</template>

<style scoped>
.auth-card {
    box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-radius: 16px;
    overflow: hidden;
}

.auth-header {
    text-align: center;
    margin-bottom: 32px;
}

.auth-title {
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 8px;
}

.auth-subtitle {
    color: #6b7280;
    font-size: 16px;
    margin: 0;
    line-height: 1.5;
}

.auth-form {
    margin-top: 24px;
}

.auth-submit-btn {
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
}

.auth-footer {
    text-align: center;
    margin-top: 16px;
}

.min-h-screen {
    min-height: 100vh;
}

.mb-6 {
    margin-bottom: 24px;
}

.mb-4 {
    margin-bottom: 16px;
}

.mt-6 {
    margin-top: 24px;
}
</style>
