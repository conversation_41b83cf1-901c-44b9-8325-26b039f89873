<script setup lang="ts">
import { SharedData } from '@/types';
import {
    CreditCardOutlined,
    DownOutlined,
    HomeOutlined,
    LoginOutlined,
    LogoutOutlined,
    PlayCircleOutlined,
    SettingOutlined,
    ShoppingOutlined,
    SwapOutlined,
    UserAddOutlined,
    UserOutlined,
    WalletOutlined,
} from '@ant-design/icons-vue';
import { Link, router, usePage } from '@inertiajs/vue3';
import { computed, ref } from 'vue';

const activeBottomTab = ref('home');

const setActiveBottomTab = (tab: string) => {
    activeBottomTab.value = tab;

    switch (tab) {
        case 'home':
            window.location.href = route('home');
            break;
        case 'buy-card':
            window.location.href = route('cards.index');
            break;
        case 'exchange-card':
            window.location.href = route('card-exchange.index');
            break;
        case 'top-up':
            break;
    }
};

const page = usePage<SharedData>();
const auth = computed(() => page.props.auth);

// User dropdown methods
const handleMenuClick = (key: string) => {
    switch (key) {
        case 'profile':
            // Navigate to profile page
            break;
        case 'wallet':
            // Navigate to wallet page
            break;
        case 'settings':
            // Navigate to settings page
            break;
        case 'logout':
            router.post(route('logout'));
    }
};
</script>

<template>
    <a-config-provider>
        <a-layout class="layout-container">
            <a-layout-header class="appbar">
                <div class="appbar-content">
                    <div class="logo-section">
                        <Link :href="route('home')">
                            <a-typography-title :level="3" class="logo-text"> MuaTheGame </a-typography-title>
                        </Link>
                    </div>

                    <div class="nav-section">
                        <a-menu
                            mode="horizontal"
                            class="main-menu"
                            :style="{
                                lineHeight: '64px',
                                borderBottom: 'none',
                                backgroundColor: 'transparent',
                            }"
                        >
                            <a-menu-item key="home">
                                <Link :href="route('home')">
                                    <template #icon>
                                        <HomeOutlined />
                                    </template>
                                    Trang Chủ
                                </Link>
                            </a-menu-item>

                            <a-menu-item key="buy-card">
                                <Link :href="route('cards.index')">
                                    <template #icon>
                                        <CreditCardOutlined />
                                    </template>
                                    Mua Thẻ Cào
                                </Link>
                            </a-menu-item>
                            <a-menu-item key="exchange-card">
                                <Link :href="route('card-exchange.index')">
                                    <template #icon>
                                        <SwapOutlined />
                                    </template>
                                    Đổi Thẻ Cào
                                </Link>
                            </a-menu-item>
                            <a-menu-item key="top-up">
                                <template #icon>
                                    <PlayCircleOutlined />
                                </template>
                                Nạp Game
                            </a-menu-item>
                        </a-menu>
                    </div>

                    <div class="action-section">
                        <a-divider type="vertical" />

                        <a-space :size="8" v-if="!auth.user">
                            <Link :href="route('login')">
                                <a-button type="default" size="middle">
                                    <template #icon>
                                        <LoginOutlined />
                                    </template>
                                    Đăng nhập
                                </a-button>
                            </Link>

                            <Link :href="route('register')">
                                <a-button type="primary" size="middle" class="btn-register">
                                    <template #icon>
                                        <UserAddOutlined />
                                    </template>
                                    Đăng ký
                                </a-button>
                            </Link>
                        </a-space>

                        <div v-if="auth.user" class="user-section">
                            <a-space :size="12">
                                <div class="user-balance">
                                    <WalletOutlined class="balance-icon" />
                                    <span class="balance-text">{{ (auth.user as any).wallet_balance?.toLocaleString() || '0' }}đ</span>
                                </div>

                                <a-dropdown placement="bottomRight" :trigger="['click']">
                                    <a-button type="text" class="user-dropdown-trigger">
                                        <a-avatar class="user-avatar">
                                            <template #icon>
                                                <UserOutlined />
                                            </template>
                                        </a-avatar>
                                        <span class="username">{{ auth.user.name }}</span>
                                        <DownOutlined class="dropdown-icon" />
                                    </a-button>

                                    <template #overlay>
                                        <a-menu @click="(e: any) => handleMenuClick(e.key)" class="user-dropdown-menu">
                                            <a-menu-item key="profile">
                                                <UserOutlined />
                                                <span style="margin-left: 10px">Thông tin cá nhân</span>
                                            </a-menu-item>
                                            <a-menu-item key="wallet">
                                                <WalletOutlined />
                                                <span style="margin-left: 10px">Ví của tôi</span>
                                            </a-menu-item>
                                            <a-menu-item key="settings">
                                                <SettingOutlined />
                                                <span style="margin-left: 10px">Cài đặt</span>
                                            </a-menu-item>
                                            <a-menu-divider />
                                            <a-menu-item key="logout" class="logout-item">
                                                <LogoutOutlined />
                                                <span style="margin-left: 10px">Đăng xuất</span>
                                            </a-menu-item>
                                        </a-menu>
                                    </template>
                                </a-dropdown>
                            </a-space>
                        </div>
                    </div>
                </div>
            </a-layout-header>

            <a-layout-content class="main-content">
                <slot />
            </a-layout-content>

            <div class="bottom-navbar">
                <a-card :bordered="false" class="bottom-nav-card" :body-style="{ padding: '0' }">
                    <a-row class="bottom-nav-items">
                        <a-col :span="6" class="bottom-nav-item" :class="{ active: activeBottomTab === 'home' }" @click="setActiveBottomTab('home')">
                            <div class="nav-item-content">
                                <HomeOutlined class="nav-icon" />
                                <span class="nav-text">Trang Chủ</span>
                            </div>
                        </a-col>

                        <a-col
                            :span="6"
                            class="bottom-nav-item"
                            :class="{ active: activeBottomTab === 'buy-card' }"
                            @click="setActiveBottomTab('buy-card')"
                        >
                            <div class="nav-item-content">
                                <ShoppingOutlined class="nav-icon" />
                                <span class="nav-text">Mua Thẻ</span>
                            </div>
                        </a-col>

                        <a-col
                            :span="6"
                            class="bottom-nav-item"
                            :class="{ active: activeBottomTab === 'top-up' }"
                            @click="setActiveBottomTab('top-up')"
                        >
                            <div class="nav-item-content">
                                <PlayCircleOutlined class="nav-icon" />
                                <span class="nav-text">Nạp Game</span>
                            </div>
                        </a-col>

                        <a-col
                            :span="6"
                            class="bottom-nav-item"
                            :class="{ active: activeBottomTab === 'exchange-card' }"
                            @click="setActiveBottomTab('exchange-card')"
                        >
                            <div class="nav-item-content">
                                <SwapOutlined class="nav-icon" />
                                <span class="nav-text">Đổi Thẻ</span>
                            </div>
                        </a-col>
                    </a-row>
                </a-card>
            </div>
        </a-layout>
    </a-config-provider>
</template>

<style scoped>
.layout-container {
    min-height: 100vh;
}

.appbar {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    padding: 0;
}

.appbar-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

.logo-section .logo-text {
    margin: 0;
    color: #1890ff;
    font-weight: 600;
}

.nav-section {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 600px;
}

.main-menu {
    width: 100%;
    justify-content: center;
}

.action-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.support-badge {
    color: #52c41a;
    font-weight: 500;
}

/* User Section Styles */
.user-section {
    display: flex;
    align-items: center;
}

.user-balance {
    display: flex;
    align-items: center;
    background: #f0f2f5;
    padding: 6px 12px;
    border-radius: 6px;
    gap: 6px;
}

.balance-icon {
    color: #1890ff;
    font-size: 14px;
}

.balance-text {
    font-weight: 600;
    color: #262626;
    font-size: 14px;
}

.user-dropdown-trigger {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    height: auto;
    border: none;
    box-shadow: none;
}

.user-dropdown-trigger:hover {
    background-color: #f5f5f5;
}

.user-avatar {
    background-color: #1890ff;
}

.username {
    font-weight: 500;
    color: #262626;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.dropdown-icon {
    font-size: 12px;
    color: #8c8c8c;
    transition: transform 0.3s ease;
}

.user-dropdown-trigger:hover .dropdown-icon {
    color: #1890ff;
}

/* User Dropdown Menu Styles */
.user-dropdown-menu {
    min-width: 200px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.user-dropdown-menu .ant-menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 16px;
    margin: 0;
    border-radius: 0;
}

.user-dropdown-menu .ant-menu-item:hover {
    background-color: #f5f5f5;
}

.user-dropdown-menu .logout-item {
    color: #ff4d4f;
}

.user-dropdown-menu .logout-item:hover {
    background-color: #fff1f0;
    color: #ff4d4f;
}

.main-content {
    padding-bottom: 0;
}

/* Bottom Navigation Bar */
.bottom-navbar {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.bottom-nav-card {
    margin: 0;
    border-radius: 0;
    box-shadow: none;
}

.bottom-nav-card .ant-card-body {
    padding: 8px 0;
}

.bottom-nav-items {
    margin: 0;
}

.bottom-nav-item {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.bottom-nav-item:hover {
    background-color: #f5f5f5;
}

.bottom-nav-item.active {
    background-color: #e6f7ff;
}

.bottom-nav-item.active .nav-icon {
    color: #1890ff;
}

.bottom-nav-item.active .nav-text {
    color: #1890ff;
    font-weight: 600;
}

.nav-item-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px 4px;
    position: relative;
}

.nav-icon {
    font-size: 20px;
    color: #666;
    margin-bottom: 4px;
    transition: color 0.3s ease;
}

.nav-text {
    font-size: 11px;
    color: #666;
    text-align: center;
    line-height: 1.2;
    transition: all 0.3s ease;
}

.nav-badge {
    position: absolute;
    top: 2px;
    right: 8px;
}

.nav-badge .ant-badge-count {
    background: #ff4d4f;
    color: white;
    font-size: 10px;
    height: 16px;
    min-width: 16px;
    line-height: 16px;
    padding: 0 4px;
    border-radius: 8px;
}

/* Responsive Design */
@media (max-width: 992px) {
    .nav-section {
        display: none;
    }
}

@media (max-width: 768px) {
    .appbar-content {
        padding: 0 16px;
    }

    .support-badge {
        display: none;
    }

    .btn-register {
        display: none;
    }

    .bottom-navbar {
        display: block;
    }

    .main-content {
        padding-bottom: 70px;
    }

    /* Hide user balance on mobile */
    .user-balance {
        display: none;
    }

    .username {
        display: none;
    }

    .user-dropdown-trigger {
        padding: 4px;
    }
}

@media (max-width: 576px) {
    .logo-section .logo-text {
        font-size: 18px;
    }

    .appbar-content {
        padding: 0 12px;
    }
}
</style>
