<template>
    <a-config-provider>
        <a-layout class="layout-container">
            <a-layout-header class="appbar">
                <div class="appbar-content">
                    <div class="logo-section">
                        <Link :href="route('home')">
                            <a-typography-title :level="3" class="logo-text"> MuaTheGame </a-typography-title>
                        </Link>
                    </div>

                    <div class="nav-section">
                        <a-menu
                            mode="horizontal"
                            class="main-menu"
                            :style="{
                                lineHeight: '64px',
                                borderBottom: 'none',
                                backgroundColor: 'transparent',
                            }"
                        >
                            <a-menu-item key="home">
                                <Link :href="route('home')">
                                    <template #icon>
                                        <HomeOutlined />
                                    </template>
                                    Trang Chủ
                                </Link>
                            </a-menu-item>

                            <a-menu-item key="buy-card">
                                <Link :href="route('cards.index')">
                                    <template #icon>
                                        <CreditCardOutlined />
                                    </template>
                                    Mua Thẻ Cào
                                </Link>
                            </a-menu-item>
                            <a-menu-item key="exchange-card">
                                <Link :href="route('card-exchange.index')">
                                    <template #icon>
                                        <SwapOutlined />
                                    </template>
                                    Đổi Thẻ Cào
                                </Link>
                            </a-menu-item>
                            <a-menu-item key="top-up">
                                <template #icon>
                                    <PlayCircleOutlined />
                                </template>
                                Nạp Game
                            </a-menu-item>
                        </a-menu>
                    </div>

                    <div class="action-section">
                        <a-divider type="vertical" />

                        <a-space :size="8">
                            <a-button type="default" size="middle">
                                <template #icon>
                                    <LoginOutlined />
                                </template>
                                Đăng nhập
                            </a-button>

                            <a-button type="primary" size="middle" class="btn-register">
                                <template #icon>
                                    <UserAddOutlined />
                                </template>
                                Đăng ký
                            </a-button>
                        </a-space>
                    </div>
                </div>
            </a-layout-header>

            <a-layout-content class="main-content">
                <slot />
            </a-layout-content>

            <div class="bottom-navbar">
                <a-card :bordered="false" class="bottom-nav-card" :body-style="{ padding: '0' }">
                    <a-row class="bottom-nav-items">
                        <a-col :span="6" class="bottom-nav-item" :class="{ active: activeBottomTab === 'home' }" @click="setActiveBottomTab('home')">
                            <div class="nav-item-content">
                                <HomeOutlined class="nav-icon" />
                                <span class="nav-text">Trang Chủ</span>
                            </div>
                        </a-col>

                        <a-col
                            :span="6"
                            class="bottom-nav-item"
                            :class="{ active: activeBottomTab === 'buy-card' }"
                            @click="setActiveBottomTab('buy-card')"
                        >
                            <div class="nav-item-content">
                                <ShoppingOutlined class="nav-icon" />
                                <span class="nav-text">Mua Thẻ</span>
                            </div>
                        </a-col>

                        <a-col
                            :span="6"
                            class="bottom-nav-item"
                            :class="{ active: activeBottomTab === 'top-up' }"
                            @click="setActiveBottomTab('top-up')"
                        >
                            <div class="nav-item-content">
                                <PlayCircleOutlined class="nav-icon" />
                                <span class="nav-text">Nạp Game</span>
                            </div>
                        </a-col>

                        <a-col
                            :span="6"
                            class="bottom-nav-item"
                            :class="{ active: activeBottomTab === 'exchange-card' }"
                            @click="setActiveBottomTab('exchange-card')"
                        >
                            <div class="nav-item-content">
                                <SwapOutlined class="nav-icon" />
                                <span class="nav-text">Đổi Thẻ</span>
                            </div>
                        </a-col>
                    </a-row>
                </a-card>
            </div>
        </a-layout>
    </a-config-provider>
</template>

<script setup lang="ts">
import {
    CreditCardOutlined,
    HomeOutlined,
    LoginOutlined,
    PlayCircleOutlined,
    ShoppingOutlined,
    SwapOutlined,
    UserAddOutlined,
} from '@ant-design/icons-vue';
import { Link } from '@inertiajs/vue3';
import { ref } from 'vue';

const activeBottomTab = ref('home');

const setActiveBottomTab = (tab: string) => {
    activeBottomTab.value = tab;

    // Navigation logic
    switch (tab) {
        case 'home':
            window.location.href = route('home');
            break;
        case 'buy-card':
            window.location.href = route('cards.index');
            break;
        case 'exchange-card':
            window.location.href = route('card-exchange.index');
            break;
        case 'top-up':
            // Add top-up route when implemented
            console.log('Top-up not implemented yet');
            break;
    }
};
</script>

<style scoped>
.layout-container {
    min-height: 100vh;
}

.appbar {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    padding: 0;
}

.appbar-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

.logo-section .logo-text {
    margin: 0;
    color: #1890ff;
    font-weight: 600;
}

.nav-section {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 600px;
}

.main-menu {
    width: 100%;
    justify-content: center;
}

.action-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.support-badge {
    color: #52c41a;
    font-weight: 500;
}

.main-content {
    padding-bottom: 0;
}

/* Bottom Navigation Bar */
.bottom-navbar {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.bottom-nav-card {
    margin: 0;
    border-radius: 0;
    box-shadow: none;
}

.bottom-nav-card .ant-card-body {
    padding: 8px 0;
}

.bottom-nav-items {
    margin: 0;
}

.bottom-nav-item {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.bottom-nav-item:hover {
    background-color: #f5f5f5;
}

.bottom-nav-item.active {
    background-color: #e6f7ff;
}

.bottom-nav-item.active .nav-icon {
    color: #1890ff;
}

.bottom-nav-item.active .nav-text {
    color: #1890ff;
    font-weight: 600;
}

.nav-item-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px 4px;
    position: relative;
}

.nav-icon {
    font-size: 20px;
    color: #666;
    margin-bottom: 4px;
    transition: color 0.3s ease;
}

.nav-text {
    font-size: 11px;
    color: #666;
    text-align: center;
    line-height: 1.2;
    transition: all 0.3s ease;
}

.nav-badge {
    position: absolute;
    top: 2px;
    right: 8px;
}

.nav-badge .ant-badge-count {
    background: #ff4d4f;
    color: white;
    font-size: 10px;
    height: 16px;
    min-width: 16px;
    line-height: 16px;
    padding: 0 4px;
    border-radius: 8px;
}

/* Responsive Design */
@media (max-width: 992px) {
    .nav-section {
        display: none;
    }
}

@media (max-width: 768px) {
    .appbar-content {
        padding: 0 16px;
    }

    .support-badge {
        display: none;
    }

    .btn-register {
        display: none;
    }

    .bottom-navbar {
        display: block;
    }

    .main-content {
        padding-bottom: 70px;
    }
}

@media (max-width: 576px) {
    .logo-section .logo-text {
        font-size: 18px;
    }

    .appbar-content {
        padding: 0 12px;
    }
}
</style>
