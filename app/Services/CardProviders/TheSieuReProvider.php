<?php

namespace App\Services\CardProviders;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class TheSieuReProvider extends CardProvider
{
    private string $apiKey;
    private string $partnerId;
    private string $baseUrl;

    public function __construct()
    {
        $this->apiKey = config('services.thesieure.api_key');
        $this->partnerId = config('services.thesieure.partner_id');
        $this->baseUrl = config('services.thesieure.url', 'https://thesieure.com/chargingws/v2');
    }

    public function getName(): string
    {
        return 'TheSieuRe';
    }

    public function exchange(array $payload): CardExchangeResponse
    {
        $this->validatePayload($payload);

        $requestId = Str::uuid()->toString();

        try {
            $requestData = [
                'request_id' => $requestId,
                'telco' => $this->mapCardType($payload['card_type']),
                'amount' => $payload['amount'],
                'serial' => $payload['serial'],
                'code' => $payload['pin'],
                'partner_id' => $this->apiKey,
                'command' => 'charging',
            ];

            $requestData['sign'] = $this->generateSignature($requestData);

            $response = Http::timeout(30)
                ->post($this->baseUrl, $requestData);

            $data = $response->json();

            return $this->parseResponse($data, $requestId);
        } catch (Exception $e) {
            return CardExchangeResponse::failed(
                'API connection error: ' . $e->getMessage(),
            );
        }
    }

    public function getExchangeRate(string $cardType, int $amount): float
    {
        try {
            $feeData = $this->getFeeData();
            $telco = $this->mapCardType($cardType);

            foreach ($feeData as $item) {
                if ($item['telco'] === $telco && $item['value'] == $amount) {
                    return 100 - $item['fees'];
                }
            }

            $rates = [
                'viettel' => 85.0,
                'mobifone' => 85.0,
                'vinaphone' => 85.0,
                'garena' => 82.0,
                'zing' => 82.0,
                'vcoin' => 80.0,
                'appota' => 80.0,
                'funcard' => 80.0,
            ];

            return $rates[strtolower($cardType)] ?? 80.0;
        } catch (\Exception $e) {
            Log::error('Error getting exchange rate from API', [
                'error' => $e->getMessage(),
                'card_type' => $cardType,
                'amount' => $amount,
            ]);

            return 80.0;
        }
    }

    public function getFeeData(): array
    {
        try {
            $response = Http::timeout(30)
                ->get($this->baseUrl . '/getfee', [
                    'partner_id' => $this->apiKey,
                ]);

            if ($response->successful()) {
                return $response->json();
            }

            throw new \Exception('API request failed: ' . $response->status());
        } catch (\Exception $e) {
            Log::error('Error fetching fee data from TheSieuRe API', [
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    public function getAvailableCardTypes(): array
    {
        try {
            $feeData = $this->getFeeData();
            $cardTypes = [];

            $groupedData = [];
            foreach ($feeData as $item) {
                $telco = $item['telco'];
                if (!isset($groupedData[$telco])) {
                    $groupedData[$telco] = [];
                }
                $groupedData[$telco][] = $item;
            }

            $fees = 10;

            foreach ($groupedData as $telco => $denominations) {
                $cardTypes[] = [
                    'telco' => $telco,
                    'name' => $this->getTelcoDisplayName($telco),
                    'slug' => strtolower($telco),
                    'image_url' => $this->getTelcoImageUrl($telco),
                    'denominations' => array_map(function ($denom) use ($fees) {
                        return [
                            'value' => $denom['value'],
                            'fees' => $fees ?? $denom['fees'],
                            'penalty' => $denom['penalty'],
                            'exchange_rate' => 100 - ($fees ?? $denom['fees']),
                            'credited_amount' => (int) ($denom['value'] * (100 - $denom['fees']) / 100),
                        ];
                    }, $denominations),
                ];
            }

            return $cardTypes;
        } catch (\Exception $e) {
            Log::error('Error getting available card types', [
                'error' => $e->getMessage(),
            ]);
            return [];
        }
    }

    private function mapCardType(string $cardType): string
    {
        $mapping = [
            'viettel' => 'VIETTEL',
            'mobifone' => 'MOBIFONE',
            'vinaphone' => 'VINAPHONE',
            'garena' => 'GARENA',
            'zing' => 'ZING',
            'vcoin' => 'VCOIN',
            'appota' => 'APPOTA',
            'funcard' => 'FUNCARD',
        ];

        return $mapping[strtolower($cardType)] ?? strtoupper($cardType);
    }

    private function getTelcoDisplayName(string $telco): string
    {
        $names = [
            'VIETTEL' => 'Thẻ Viettel',
            'MOBIFONE' => 'Thẻ Mobifone',
            'VINAPHONE' => 'Thẻ Vinaphone',
            'VNMOBI' => 'Thẻ Vietnamobile',
            'GARENA' => 'Thẻ Garena',
            'ZING' => 'Thẻ Zing',
            'VCOIN' => 'Thẻ VCoin',
            'APPOTA' => 'Thẻ Appota',
            'FUNCARD' => 'Thẻ Funcard',
            'GATE' => 'Thẻ Gate',
            'SCOIN' => 'Thẻ Scoin',
            'CAROT' => 'Thẻ Carot',
        ];

        return $names[$telco] ?? 'Thẻ ' . ucfirst(strtolower($telco));
    }

    private function getTelcoImageUrl(string $telco): string
    {
        $images = [
            'VIETTEL' => 'card-type/viettel.png',
            'MOBIFONE' => 'card-type/mobifone.png',
            'VINAPHONE' => 'card-type/vinaphone.png',
            'VNMOBI' => 'card-type/vietnamobile.png',
            'GARENA' => 'card-type/garena.png',
            'ZING' => 'card-type/zing.png',
            'VCOIN' => 'card-type/vcoin.png',
            'APPOTA' => 'card-type/appota.png',
            'FUNCARD' => 'card-type/funcard.png',
            'GATE' => 'card-type/gate.png',
            'SCOIN' => 'card-type/scoin.png',
            'CAROT' => 'card-type/carot.png',
        ];

        return $images[$telco] ?? 'https://napsieutoc.com/files/the-cao/default.png';
    }

    private function generateSignature(array $data): string
    {
        return md5($this->partnerId . $data['code'] . $data['serial']);
    }
}
