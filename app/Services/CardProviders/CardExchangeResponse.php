<?php

namespace App\Services\CardProviders;

class CardExchangeResponse
{
    public function __construct(
        public bool $success,
        public ?string $transactionId = null,
        public ?int $actualValue = null,
        public ?int $creditedAmount = null,
        public ?string $message = null,
        public ?array $rawResponse = null,
        public ?string $status = null,
        public ?string $requestId = null,
    ) {}

    public static function success(
        string $transactionId,
        int $actualValue,
        int $creditedAmount,
        array $rawResponse = [],
        ?string $requestId = null,
    ): self {
        return new self(
            success: true,
            transactionId: $transactionId,
            actualValue: $actualValue,
            creditedAmount: $creditedAmount,
            rawResponse: $rawResponse,
            status: 'success',
            requestId: $requestId,
        );
    }

    public static function pending(
        string $transactionId,
        array $rawResponse = [],
        ?string $requestId = null,
    ): self {
        return new self(
            success: false,
            transactionId: $transactionId,
            rawResponse: $rawResponse,
            status: 'pending',
            requestId: $requestId,
        );
    }

    public static function failed(
        string $message,
        array $rawResponse = [],
        ?string $transactionId = null,
        ?string $requestId = null,
    ): self {
        return new self(
            success: false,
            transactionId: $transactionId,
            message: $message,
            rawResponse: $rawResponse,
            status: 'failed',
            requestId: $requestId,
        );
    }

    public static function invalidCard(
        string $message,
        array $rawResponse = [],
        ?string $transactionId = null,
        ?string $requestId = null,
    ): self {
        return new self(
            success: false,
            transactionId: $transactionId,
            message: $message,
            rawResponse: $rawResponse,
            status: 'invalid_card',
            requestId: $requestId,
        );
    }
}
