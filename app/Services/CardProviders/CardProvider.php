<?php

namespace App\Services\CardProviders;

abstract class CardProvider
{
    abstract public function exchange(array $payload): CardExchangeResponse;

    abstract public function getExchangeRate(string $cardType, int $amount): float;

    abstract public function getName(): string;

    abstract public function getAvailableCardTypes(): array;

    protected function validatePayload(array $payload): void
    {
        $required = ['card_type', 'serial', 'pin', 'amount'];

        foreach ($required as $field) {
            if (!isset($payload[$field]) || empty($payload[$field])) {
                throw new \InvalidArgumentException("Missing required field: {$field}");
            }
        }
    }

    public function parseResponse(array $data, ?string $requestId = null): CardExchangeResponse
    {
        $status = $data['status'] ?? null;
        $transactionId = $data['trans_id'] ?? null;

        $message = match ($data['message'] ?? null) {
            'lang.invalid_card_code' => 'Mã thẻ không hợp lệ',
            'INVALID_CARD' => 'Số seri hoặc mã thẻ không hợp lệ',
            default => $data['message'] ?? 'Unknown error',
        };

        switch ($status) {
            case 1:
                return CardExchangeResponse::success(
                    transactionId: $transactionId,
                    actualValue: $data['value'] ?? 0,
                    creditedAmount: $data['amount'] ?? 0,
                    rawResponse: $data,
                    requestId: $requestId,
                );

            case 2:
                return CardExchangeResponse::success(
                    transactionId: $transactionId,
                    actualValue: $data['value'] ?? 0,
                    creditedAmount: $data['amount'] ?? 0,
                    rawResponse: $data,
                    requestId: $requestId,
                );

            case 3:
                return CardExchangeResponse::invalidCard(
                    message: $message,
                    rawResponse: $data,
                    transactionId: $transactionId,
                    requestId: $requestId,
                );

            case 4:
                return CardExchangeResponse::failed(
                    message: $message ?: 'Hệ thống bảo trì',
                    rawResponse: $data,
                    transactionId: $transactionId,
                    requestId: $requestId,
                );

            case 99:
                return CardExchangeResponse::pending(
                    transactionId: $transactionId,
                    rawResponse: $data,
                    requestId: $requestId,
                );

            case 100:
                return CardExchangeResponse::failed(
                    message: $message ?: 'Gửi thẻ thất bại',
                    rawResponse: $data,
                    transactionId: $transactionId,
                    requestId: $requestId,
                );

            default:
                return CardExchangeResponse::failed(
                    message: $message,
                    rawResponse: $data,
                    transactionId: $transactionId,
                    requestId: $requestId,
                );
        }
    }
}
