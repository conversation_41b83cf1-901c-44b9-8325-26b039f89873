<?php

namespace App\Services;

use App\Models\CardTransaction;
use App\Models\User;
use App\Services\CardProviders\CardExchangeResponse;
use App\Services\CardProviders\CardProvider;
use App\Services\CardProviders\TheSieuReProvider;
use Exception;
use Illuminate\Support\Facades\DB;

class CardExchangeService
{
    private CardProvider $provider;

    public function __construct()
    {
        $this->provider = new TheSieuReProvider();
    }

    public function processExchange(User $user, array $cardData): CardTransaction
    {
        $availableCards = $this->provider->getAvailableCardTypes();
        $selectedCard = null;
        $selectedDenomination = null;

        foreach ($availableCards as $card) {
            if ($card['telco'] === $cardData['telco']) {
                $selectedCard = $card;
                foreach ($card['denominations'] as $denom) {
                    if ($denom['value'] == $cardData['value']) {
                        $selectedDenomination = $denom;
                        break;
                    }
                }
                break;
            }
        }

        if (! $selectedCard || ! $selectedDenomination) {
            throw new Exception('Loại thẻ hoặc mệnh giá không được hỗ trợ');
        }

        $this->checkDuplicateSubmission($user, $cardData['serial'], $cardData['pin']);

        DB::beginTransaction();

        try {
            $response = $this->provider->exchange([
                'card_type' => $selectedCard['slug'],
                'serial' => $cardData['serial'],
                'pin' => $cardData['pin'],
                'amount' => $selectedDenomination['value'],
            ]);

            if ($response->status === 'pending' || $response->status === 'success') {
                $transaction = CardTransaction::query()->create([
                    'user_id' => $user->id,
                    'telco' => $selectedCard['telco'],
                    'card_name' => $selectedCard['name'],
                    'serial' => $cardData['serial'],
                    'pin' => $cardData['pin'],
                    'declared_value' => $selectedDenomination['value'],
                    'exchange_rate' => $selectedDenomination['exchange_rate'],
                    'status' => 'pending',
                ]);
            } else {
                throw new Exception($response->message);
            }

            $this->updateTransactionFromResponse($transaction, $response);

            DB::commit();

            return $transaction;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function getAvailableCardTypes(): array
    {
        return $this->provider->getAvailableCardTypes();
    }

    public function updateTransactionStatus(CardTransaction $transaction, array $data): void
    {
        if (! $transaction->provider_transaction_id) {
            return;
        }

        $response = $this->provider->parseResponse($data);

        $this->updateTransactionFromResponse($transaction, $response);
    }

    public function getExchangeRate(string $cardTypeSlug, int $amount): float
    {
        return $this->provider->getExchangeRate($cardTypeSlug, $amount);
    }

    private function checkDuplicateSubmission(User $user, string $serial, string $pin): void
    {
        $recentSubmission = CardTransaction::query()
            ->where('user_id', $user->id)
            ->where('serial', $serial)
            ->where('pin', $pin)
            ->where('created_at', '>', now()->subMinutes(5))
            ->first();

        if ($recentSubmission) {
            throw new Exception('Phát hiện thẻ đã được gửi trong vòng 5 phút trước đó. Vui lòng chờ 5 phút trước khi thử lại.');
        }
    }

    private function updateTransactionFromResponse(CardTransaction $transaction, CardExchangeResponse $response): void
    {
        $transaction->update([
            'provider_transaction_id' => $response->transactionId,
            'request_id' => $response->requestId,
            'status' => $response->status ?? 'processing',
            'provider_response' => $response->rawResponse,
            'failure_reason' => $response->message,
        ]);

        if ($response->success && $response->status === 'success') {
            $transaction->update([
                'actual_value' => $response->actualValue,
                'credited_amount' => $response->creditedAmount,
                'processed_at' => now(),
            ]);

            $transaction->user->addToWallet($response->creditedAmount);
        }
    }
}
