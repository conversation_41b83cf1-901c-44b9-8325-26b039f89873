<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CardDenomination extends Model
{
    protected $fillable = [
        'card_type_id',
        'value',
        'discount_percentage',
        'status',
    ];

    protected $casts = [
        'value' => 'integer',
        'discount_percentage' => 'decimal:2',
        'status' => 'boolean',
    ];

    public function cardType(): BelongsTo
    {
        return $this->belongsTo(CardType::class);
    }

    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    public function getDiscountedPriceAttribute(): int
    {
        return $this->value - ($this->value * $this->discount_percentage / 100);
    }
}
