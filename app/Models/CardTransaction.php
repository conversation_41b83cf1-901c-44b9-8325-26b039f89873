<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CardTransaction extends Model
{
    protected $fillable = [
        'user_id',
        'telco',
        'card_name',
        'serial',
        'pin',
        'declared_value',
        'actual_value',
        'exchange_rate',
        'credited_amount',
        'provider_transaction_id',
        'request_id',
        'status',
        'provider_response',
        'failure_reason',
        'processed_at',
    ];

    protected $casts = [
        'declared_value' => 'integer',
        'actual_value' => 'integer',
        'exchange_rate' => 'decimal:2',
        'credited_amount' => 'integer',
        'provider_response' => 'array',
        'processed_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeProcessing($query)
    {
        return $query->where('status', 'processing');
    }

    public function scopeSuccess($query)
    {
        return $query->where('status', 'success');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function isSuccess(): bool
    {
        return $this->status === 'success';
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isProcessing(): bool
    {
        return $this->status === 'processing';
    }

    public function isFailed(): bool
    {
        return in_array($this->status, ['failed', 'invalid_card']);
    }
}
