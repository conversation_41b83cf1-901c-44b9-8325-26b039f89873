<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CardType extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'image_url',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    public function denominations(): HasMany
    {
        return $this->hasMany(CardDenomination::class);
    }

    public function activeDenominations(): HasMany
    {
        return $this->hasMany(CardDenomination::class)->where('status', true);
    }

    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    public function scopeActive($query)
    {
        return $query->where('status', true);
    }
}
