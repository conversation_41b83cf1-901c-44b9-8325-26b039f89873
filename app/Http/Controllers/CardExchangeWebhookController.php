<?php

namespace App\Http\Controllers;

use App\Models\CardTransaction;
use App\Services\CardExchangeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CardExchangeWebhookController extends Controller
{
    public function __invoke(Request $request, CardExchangeService $exchangeService)
    {
        Log::info('TheSieuRe webhook received', $request->all());

        // Verify webhook signature
        if (!$this->verifySignature($request)) {
            Log::warning('Invalid webhook signature', $request->all());
            return response()->json(['error' => 'Invalid signature'], 400);
        }

        $transactionId = $request->input('trans_id');

        if (!$transactionId) {
            Log::warning('Missing transaction ID in webhook', $request->all());
            return response()->json(['error' => 'Missing transaction ID'], 400);
        }

        // Find transaction by provider transaction ID
        $transaction = CardTransaction::where('provider_transaction_id', $transactionId)->first();

        if (!$transaction) {
            Log::warning('Transaction not found for webhook', [
                'provider_transaction_id' => $transactionId,
                'webhook_data' => $request->all(),
            ]);
            return response()->json(['error' => 'Transaction not found'], 404);
        }

        try {
            // Update transaction status based on webhook
            $exchangeService->updateTransactionStatus($transaction);

            Log::info('Transaction updated via webhook', [
                'transaction_id' => $transaction->id,
                'provider_transaction_id' => $transactionId,
                'status' => $transaction->status,
            ]);

            return response()->json(['success' => true]);

        } catch (\Exception $e) {
            Log::error('Error processing webhook', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
                'webhook_data' => $request->all(),
            ]);

            return response()->json(['error' => 'Processing failed'], 500);
        }
    }

    private function verifySignature(Request $request): bool
    {
        $apiKey = config('services.thesieure.api_key');

        if (!$apiKey) {
            return false;
        }

        $data = $request->all();
        $receivedSignature = $data['sign'] ?? '';
        unset($data['sign']);

        ksort($data);
        $dataStr = '';

        foreach ($data as $value) {
            $dataStr .= $value;
        }

        $expectedSignature = md5($apiKey . $dataStr);

        return hash_equals($expectedSignature, $receivedSignature);
    }
}
