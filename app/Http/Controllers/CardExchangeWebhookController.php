<?php

namespace App\Http\Controllers;

use App\Models\CardTransaction;
use App\Services\CardExchangeService;
use Exception;
use Illuminate\Http\Request;

class CardExchangeWebhookController extends Controller
{
    public function __invoke(Request $request, CardExchangeService $exchangeService)
    {
        $request->validate([
            'trans_id' => 'required',
            'code' => 'required|string',
            'serial' => 'required|string',
            'callback_sign' => 'required|string',
        ]);

        if (! $this->verifySignature($request)) {
            return response()->json(['error' => 'Invalid signature'], 400);
        }

        $transactionId = $request->input('trans_id');

        if (! $transactionId) {
            return response()->json(['error' => 'Missing transaction ID'], 400);
        }

        $transaction = CardTransaction::query()->where('provider_transaction_id', $transactionId)->first();

        if (! $transaction) {
            return response()->json(['error' => 'Transaction not found'], 404);
        }

        try {
            $exchangeService->updateTransactionStatus($transaction, $request->all());

            return response()->json(['success' => true]);
        } catch (Exception) {
            return response()->json(['error' => 'Processing failed'], 500);
        }
    }

    private function verifySignature(Request $request): bool
    {
        $partnerId = config('services.thesieure.partner_id');

        $expectedSignature = md5($partnerId . $request->input('code') . $request->input('serial'));

        return hash_equals($expectedSignature, $request->input('callback_sign'));
    }
}
